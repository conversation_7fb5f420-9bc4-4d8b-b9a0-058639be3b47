const express = require('express');

// Import meal plan route handlers
const generateMeals = require('./generateMeals');
const replaceMeal = require('./replaceMeal');
const generateImage = require('./generateImage');

const router = express.Router();

// Register meal plan routes
const routes = [
  generateMeals,
  replaceMeal,
  generateImage,
];

routes.forEach((route) => {
  const {method, path, middleware = [], handler} = route;

  // Apply middleware and handler based on HTTP method
  switch (method.toLowerCase()) {
  case 'get':
    router.get(path, ...middleware, handler);
    break;
  case 'post':
    router.post(path, ...middleware, handler);
    break;
  case 'put':
    router.put(path, ...middleware, handler);
    break;
  case 'delete':
    router.delete(path, ...middleware, handler);
    break;
  case 'patch':
    router.patch(path, ...middleware, handler);
    break;
  default:
    console.warn(`Unsupported HTTP method: ${method} for route: ${path}`);
  }
});

module.exports = router;
