const {authenticateUser} = require('../../middleware/auth');
const {with<PERSON><PERSON>po<PERSON><PERSON><PERSON><PERSON>, createApiError, createSuccessResponse} = require('../../middleware/responseHandler');
const {mealPlanGenerationSchema} = require('../../utils/validators');
const {
  validateUserProfileForMealGeneration,
  generateAndSaveMealPlan,
} = require('../../services/mealPlanGenerationService');

/**
 * POST /meal-plans/generate
 * Authenticated version of meal plan generation
 * Requires authentication - gets preferences from user profile
 * Returns immediately with success status, processes meal generation in background
 */
async function generateMealsHandler(req, res, validatedData) {
  const authenticatedUserId = req.user.uid; // From auth middleware

  console.log('Starting meal plan generation for authenticated user:', authenticatedUserId);

  console.log('Starting background meal plan generation for user:', authenticatedUserId);

  // Validate user profile exists and has required preferences BEFORE starting background processing
  const validation = await validateUserProfileForMealGeneration(authenticatedUserId);
  if (!validation.isValid) {
    throw createApiError(validation.error, 404, 'USER_NOT_FOUND');
  }

  console.log('Validation successful, starting background meal plan generation');

  // Start background processing (don't await) - validation already done
  processBackgroundMealGeneration(authenticatedUserId, validation.preferences, validation.duration);

  // Return success response (handled by response middleware)
  return createSuccessResponse(
    null,
    'تم بدء إنشاء خطة الوجبات في الخلفية',
  );
}

/**
 * Process meal generation in background and save directly to Firestore
 */
async function processBackgroundMealGeneration(userId, preferences, duration) {
  try {
    console.log('Background processing: Generating meal plan for user:', userId);

    // Use service function to handle the complete meal plan generation and saving
    await generateAndSaveMealPlan(userId, preferences, duration);
  } catch (error) {
    console.error('Background processing: Error generating and saving meal plan for user:', userId, error);
  }
}


module.exports = {
  method: 'post',
  path: '/meal-plans/generate',
  middleware: [authenticateUser],
  handler: withResponseHandler(generateMealsHandler, mealPlanGenerationSchema),
};
