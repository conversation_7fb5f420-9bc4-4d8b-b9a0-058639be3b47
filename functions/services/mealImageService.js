const OpenAIService = require('./openaiService');
const {initializeFirebase} = require('../config/firebase');
const {createNotification, NOTIFICATION_TYPES} = require('./notificationService');
const {checkAndSetBackgroundJob, clearBackgroundJob} = require('../utils/firestoreHelpers');
const {Timestamp, FieldValue} = require('firebase-admin/firestore');
// Note: fetch is available globally in Node.js 18+

/**
 * Generate image for a meal using OpenAI DALL-E
 * @param {string} userId - User ID
 * @param {string} dayDocumentId - Day document ID (optional if mealDate is provided)
 * @param {string} mealId - Meal ID
 * @param {string} mealDate - Meal date in YYYY-MM-DD format (optional if dayDocumentId is provided)
 */
async function processImageGenerationInBackground(userId, dayDocumentId, mealId, mealDate = null) {
  try {
    console.log('Processing meal image generation in background for user:', userId, {
      dayDocumentId,
      mealId,
      mealDate,
    });

    // Check and set background job to prevent concurrent image generation for the same meal
    const jobKey = `meal_image_${mealId}`;
    await checkAndSetBackgroundJob(
      userId,
      jobKey,
      'MEAL_IMAGE_GENERATION',
      10, // 10 minutes timeout for image generation
      'يتم حالياً إنشاء صورة لهذه الوجبة. يرجى الانتظار حتى اكتمالها.',
    );

    try {
      // Get the meal data from Firestore
      let mealData;
      let actualDayDocumentId = dayDocumentId;

      if (dayDocumentId) {
        // Use dayDocumentId if provided
        mealData = await getMealFromFirestore(userId, dayDocumentId, mealId);
      } else if (mealDate) {
        // Use date to find the meal if dayDocumentId is not provided
        const result = await getMealFromFirestoreByDate(userId, mealDate, mealId);
        if (result) {
          mealData = result.meal;
          actualDayDocumentId = result.dayDocumentId;
        }
      } else {
        throw new Error('Either dayDocumentId or mealDate must be provided');
      }

      if (!mealData) {
        throw new Error('Meal not found');
      }

      console.log('Found meal for image generation:', {
        mealName: mealData.name,
        actualDayDocumentId,
      });

      // Generate image prompt from meal data
      const imagePrompt = createImagePrompt(mealData);
      console.log('Image prompt created:', imagePrompt.substring(0, 200) + '...');

      // Get user profile to determine preferred language
      const admin = initializeFirebase();
      const db = admin.firestore();
      const userDoc = await db.collection('users').doc(userId).get();
      const userData = userDoc.data();
      const preferredLanguage = userData?.preferred_language || 'ar';

      // Generate image using OpenAI service
      const imageUrl = await generateMealImage(imagePrompt, preferredLanguage);
      console.log('Image generated successfully:', imageUrl);

      // Upload image to Firebase Storage
      const storageUrl = await uploadImageToStorage(userId, mealId, imageUrl);
      console.log('Image uploaded to Firebase Storage:', storageUrl);

      // Update meal with image URL
      await updateMealWithImage(userId, actualDayDocumentId, mealId, storageUrl);

      // Add image generation tracker
      const generationId = `img_gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      await addImageGenerationTracker(userId, mealId, generationId);

      // Create notification for image generation completion
      await createMealImageNotification(userId, mealData.name, dayDocumentId, mealId);

      // Clear background job tracker
      await clearBackgroundJob(userId, jobKey);

      console.log('Meal image generation completed successfully for user:', userId);
    } catch (error) {
      // Clear background job tracker on error
      await clearBackgroundJob(userId, jobKey);
      throw error;
    }
  } catch (error) {
    console.error('Error processing meal image generation in background:', error);
    throw error;
  }
}

/**
 * Get meal data from Firestore using day document ID
 * @param {string} userId - User ID
 * @param {string} dayDocumentId - Day document ID
 * @param {string} mealId - Meal ID
 * @returns {Object|null} Meal data or null if not found
 */
async function getMealFromFirestore(userId, dayDocumentId, mealId) {
  try {
    const admin = initializeFirebase();
    const dayDocRef = admin.firestore()
      .collection('users')
      .doc(userId)
      .collection('days')
      .doc(dayDocumentId);

    const dayDoc = await dayDocRef.get();
    if (!dayDoc.exists) {
      return null;
    }

    const dayData = dayDoc.data();
    const meals = dayData.meals || [];

    // Find the meal by ID
    const meal = meals.find((m) => m.id === mealId);
    return meal || null;
  } catch (error) {
    console.error('Error getting meal from Firestore:', error);
    throw error;
  }
}

/**
 * Get meal data from Firestore using date (when dayDocumentId is not available)
 * @param {string} userId - User ID
 * @param {string} mealDate - Meal date in YYYY-MM-DD format
 * @param {string} mealId - Meal ID
 * @returns {Object|null} Object containing meal data and dayDocumentId, or null if not found
 */
async function getMealFromFirestoreByDate(userId, mealDate, mealId) {
  try {
    const admin = initializeFirebase();

    // Parse the date and create start/end of day timestamps
    const targetDate = new Date(mealDate + 'T00:00:00.000Z');
    const startOfDay = Timestamp.fromDate(targetDate);
    const endOfDay = Timestamp.fromDate(new Date(targetDate.getTime() + 24 * 60 * 60 * 1000));

    console.log('Searching for meal by date:', {
      mealDate,
      mealId,
      startOfDay: startOfDay.toDate().toISOString(),
      endOfDay: endOfDay.toDate().toISOString(),
    });

    // Query for the day document with the matching date
    const daysQuery = admin.firestore()
      .collection('users')
      .doc(userId)
      .collection('days')
      .where('date', '>=', startOfDay)
      .where('date', '<', endOfDay)
      .limit(1);

    const querySnapshot = await daysQuery.get();

    if (querySnapshot.empty) {
      console.log('No day document found for date:', mealDate);
      return null;
    }

    const dayDoc = querySnapshot.docs[0];
    const dayData = dayDoc.data();
    const meals = dayData.meals || [];

    // Find the meal by ID
    const meal = meals.find((m) => m.id === mealId);

    if (!meal) {
      console.log('Meal not found in day document:', mealId);
      return null;
    }

    return {
      meal,
      dayDocumentId: dayDoc.id,
    };
  } catch (error) {
    console.error('Error getting meal from Firestore by date:', error);
    throw error;
  }
}

/**
 * Create image prompt from meal data
 * @param {Object} mealData - Meal data
 * @returns {string} Image prompt
 */
function createImagePrompt(mealData) {
  const mealName = mealData.name || 'Unknown Meal';
  const ingredients = mealData.ingredients || [];

  // Extract ingredient names and instructions
  const ingredientDescriptions = ingredients.map((ingredient) => {
    const name = ingredient.name || 'Unknown ingredient';
    const instructions = ingredient.instructions || [];
    const preparation = instructions.length > 0 ? ` (${instructions.join(', ')})` : '';
    return `${name}${preparation}`;
  }).join(', ');

  // Create a detailed prompt for DALL-E
  const prompt = `A beautifully plated ${mealName} featuring ${ingredientDescriptions}. Top-down view, overhead shot of the dish. The food should look appetizing, professionally photographed, with good lighting and attractive presentation. The food should be the main focus, arranged on a clean plate with a neutral background. High quality, restaurant-style food photography. No text, no labels, no writing visible in the image.`;

  return prompt;
}

/**
 * Generate meal image using OpenAI DALL-E
 * @param {string} prompt - Image prompt
 * @param {string} preferredLanguage - User's preferred language
 * @returns {string} Generated image URL
 */
async function generateMealImage(prompt, preferredLanguage = 'ar') {
  try {
    console.log('Starting meal image generation...', {
      promptLength: prompt.length,
      preferredLanguage,
    });
    const startTime = Date.now();

    console.log('Image prompt created, length:', prompt.length);

    // Generate meal image using abstract OpenAI service
    const imageUrl = await OpenAIService.createMealImage(prompt, preferredLanguage);

    const totalTime = Date.now();
    console.log('Meal image generation completed in:', totalTime - startTime, 'ms');

    return imageUrl;
  } catch (error) {
    console.error('Error in generateMealImage service:', error);
    throw error;
  }
}

/**
 * Upload image to Firebase Storage
 * @param {string} userId - User ID
 * @param {string} mealId - Meal ID
 * @param {string} imageUrl - Generated image URL
 * @returns {string} Firebase Storage URL
 */
async function uploadImageToStorage(userId, mealId, imageUrl) {
  try {
    const admin = initializeFirebase();
    const bucket = admin.storage().bucket();

    // Download image from OpenAI URL
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.statusText}`);
    }

    const imageBuffer = Buffer.from(await response.arrayBuffer());

    // Create file path in Storage
    const fileName = `${mealId}_${Date.now()}.png`;
    const filePath = `users/${userId}/meals/${fileName}`;
    const file = bucket.file(filePath);

    // Upload image to Storage
    await file.save(imageBuffer, {
      metadata: {
        contentType: 'image/png',
        metadata: {
          userId: userId,
          mealId: mealId,
          generatedAt: new Date().toISOString(),
        },
      },
    });

    // Make file publicly readable
    await file.makePublic();

    // Try to get Firebase Storage download URL first (better CORS support)
    try {
      const [metadata] = await file.getMetadata();
      if (metadata.mediaLink) {
        // Use Firebase Storage download URL format
        const downloadUrl = `https://firebasestorage.googleapis.com/v0/b/${bucket.name}/o/${encodeURIComponent(filePath)}?alt=media`;
        console.log('Image uploaded to Firebase Storage with download URL:', downloadUrl);
        return downloadUrl;
      }
    } catch (downloadUrlError) {
      console.log('Could not get download URL, falling back to public URL:', downloadUrlError.message);
    }

    // Fallback to public URL (CORS is now configured at bucket level)
    const publicUrl = `https://storage.googleapis.com/${bucket.name}/${filePath}`;

    console.log('Image uploaded to Firebase Storage with public URL:', publicUrl);
    return publicUrl;
  } catch (error) {
    console.error('Error uploading image to Firebase Storage:', error);
    throw error;
  }
}

/**
 * Update meal with generated image URL
 * @param {string} userId - User ID
 * @param {string} dayDocumentId - Day document ID
 * @param {string} mealId - Meal ID
 * @param {string} imageUrl - Generated image URL
 */
async function updateMealWithImage(userId, dayDocumentId, mealId, imageUrl) {
  try {
    const admin = initializeFirebase();
    const dayDocRef = admin.firestore()
      .collection('users')
      .doc(userId)
      .collection('days')
      .doc(dayDocumentId);

    const dayDoc = await dayDocRef.get();
    if (!dayDoc.exists) {
      throw new Error('Day document does not exist');
    }

    const dayData = dayDoc.data();
    const meals = dayData.meals || [];

    // Find and update the meal
    const mealIndex = meals.findIndex((meal) => meal.id === mealId);
    if (mealIndex === -1) {
      throw new Error('Meal with specified ID not found');
    }

    // Update meal with image URL
    meals[mealIndex] = {
      ...meals[mealIndex],
      image_url: imageUrl,
      image_generated_at: Timestamp.now(),
    };

    // Update the day document
    await dayDocRef.update({
      meals: meals,
      last_modified: FieldValue.serverTimestamp(),
    });

    console.log('Meal updated with image URL successfully');
  } catch (error) {
    console.error('Error updating meal with image URL:', error);
    throw error;
  }
}

/**
 * Add image generation tracker indicator to subcollection
 * @param {string} userId - User ID
 * @param {string} mealId - Meal ID
 * @param {string} generationId - Generation ID
 */
async function addImageGenerationTracker(userId, mealId, generationId) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    const trackerDocRef = db.collection('users')
      .doc(userId)
      .collection('image_generation_trackers')
      .doc(mealId);

    const trackerData = {
      datetime: Timestamp.now(),
      id: generationId,
      meal_id: mealId,
      status: 'completed',
    };

    await trackerDocRef.set(trackerData);
    console.log('Image generation tracker created for user:', userId, 'mealId:', mealId, 'generationId:', generationId);
  } catch (error) {
    console.error('Error adding image generation tracker:', error);
    throw error;
  }
}

/**
 * Create notification for meal image generation completion using notification service
 * @param {string} userId - User ID
 * @param {string} mealName - Name of the meal
 * @param {string} dayDocumentId - Day document ID
 * @param {string} mealId - Meal ID
 */
async function createMealImageNotification(userId, mealName, dayDocumentId, mealId) {
  try {
    console.log('Creating meal image notification for user:', userId);

    // Create notification using the generic notification service
    await createNotification(userId, {
      type: NOTIFICATION_TYPES.MEAL_IMAGE_READY,
      content: {
        mealName: mealName,
      },
      action: {
        type: 'internalNavigation',
        route: '/meal-details',
        parameters: {
          dayDocumentId: dayDocumentId,
          mealId: mealId,
          showImage: true,
        },
      },
      customId: 'meal_image',
    });

    console.log('Meal image notification created successfully for user:', userId);
  } catch (error) {
    console.error('Error creating meal image notification:', error);
    // Don't throw error to avoid breaking the main image generation flow
  }
}

module.exports = {
  processImageGenerationInBackground,
  getMealFromFirestore,
  getMealFromFirestoreByDate,
};
