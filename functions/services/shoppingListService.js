const {initializeFirebase} = require('../config/firebase');
const {Timestamp} = require('firebase-admin/firestore');
const OpenAIService = require('./openaiService');
const {createShoppingListPrompt} = require('../utils/prompts');
const {parseShoppingListResponse, sanitizeDataForFirestore} = require('../utils/parsers');
const {mapArabicNameToCategoryId, getDefaultIconForCategory} = require('../utils/shoppingListHelpers');
const {createNotification, formatDateRange, NOTIFICATION_TYPES} = require('./notificationService');
const {generateUserSubcollectionDocumentId, checkAndSetBackgroundJob, clearBackgroundJob} = require('../utils/firestoreHelpers');

/**
 * Generate shopping list from meals in a date range
 * @param {string} userId - User ID
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {string} shoppingIdToReplace - Optional shopping list ID to replace
 * @return {Object} Generated shopping list data
 */
async function generateShoppingListFromMeals(userId, startDate, endDate, shoppingIdToReplace = null) {
  try {
    console.log('Starting shopping list generation for user:', userId);
    console.log('Date range:', startDate, 'to', endDate);
    console.log('Replacement ID:', shoppingIdToReplace || 'None (new shopping list)');

    // Check for existing background job and set new job tracker
    await checkAndSetBackgroundJob(
      userId,
      'shopping_list_generation',
      'إنشاء قائمة تسوق',
      5,
      'يوجد عملية إنشاء قائمة تسوق قيد التنفيذ حالياً. يرجى الانتظار {remainingMinutes} دقيقة أخرى قبل المحاولة مرة أخرى.',
    );

    // Handle replacement logic if shopping_id provided
    let replacementCounter = 0;
    if (shoppingIdToReplace) {
      replacementCounter = await handleShoppingListReplacement(userId, shoppingIdToReplace);
    }

    // Fetch user's preferred language from profile
    const userPreferredLanguage = await fetchUserPreferredLanguage(userId);
    console.log('User preferred language:', userPreferredLanguage);

    // Fetch meals from the specified date range (validation already done in route)
    const mealsData = await fetchMealsFromDateRange(userId, startDate, endDate);
    console.log('Found', mealsData.length, 'meals in date range');

    // Generate shopping list using OpenAI
    const shoppingListData = await generateShoppingListWithOpenAI(userId, mealsData, startDate, endDate, userPreferredLanguage);

    // Add replacement counter to shopping list data
    shoppingListData.replacement_counter = replacementCounter;
    shoppingListData.is_replacement = shoppingIdToReplace !== null;
    if (shoppingIdToReplace) {
      shoppingListData.replaced_shopping_list_id = shoppingIdToReplace;
    }

    // Save shopping list to Firestore
    await saveShoppingListToFirestore(userId, shoppingListData);

    console.log('Successfully generated and saved shopping list for user:', userId);

    // Create notification for shopping list completion
    await createShoppingListCompletionNotification(userId, shoppingListData.title, startDate, endDate, shoppingListData.id);

    // Clean up background job tracker
    await clearBackgroundJob(userId, 'shopping_list_generation');

    return shoppingListData;
  } catch (error) {
    console.error('Error in generateShoppingListFromMeals service:', error);

    // Clean up background job tracker on error
    await clearBackgroundJob(userId, 'shopping_list_generation');

    throw error;
  }
}

/**
 * Fetch user's preferred language from Firestore
 * @param {string} userId - User ID
 * @return {string} User's preferred language
 */
async function fetchUserPreferredLanguage(userId) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    // Try to get user preferences from Firestore
    const userDoc = await db.collection('users').doc(userId).get();

    if (userDoc.exists) {
      const userData = userDoc.data();
      const preferences = userData.preferences || {};
      const preferredLanguage = preferences.preferred_language || userData.preferred_language;

      if (preferredLanguage) {
        console.log('Found user preferred language in Firestore:', preferredLanguage);
        return preferredLanguage;
      }
    }

    // Default to Arabic if not found
    console.log('User preferred language not found, defaulting to Arabic');
    return 'ar';
  } catch (error) {
    console.error('Error fetching user preferred language:', error);
    // Default to Arabic on error
    return 'ar';
  }
}

/**
 * Fetch meals from Firestore for the specified date range
 * @param {string} userId - User ID
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @return {Array} Array of meals data
 */
async function fetchMealsFromDateRange(userId, startDate, endDate) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    // Create proper date range for query
    // Set start date to beginning of day (00:00:00)
    const queryStartDate = new Date(startDate);
    queryStartDate.setHours(0, 0, 0, 0);

    // Set end date to end of day (23:59:59.999)
    const queryEndDate = new Date(endDate);
    queryEndDate.setHours(23, 59, 59, 999);

    console.log('Querying meals from:', queryStartDate.toISOString(), 'to:', queryEndDate.toISOString());

    // Query meals within the date range
    const mealsSnapshot = await db
      .collection('users')
      .doc(userId)
      .collection('days')
      .where('date', '>=', Timestamp.fromDate(queryStartDate))
      .where('date', '<=', Timestamp.fromDate(queryEndDate))
      .orderBy('date', 'asc')
      .get();

    console.log('Found', mealsSnapshot.size, 'day documents in query');

    // If no meals found, get available date range for better error message
    if (mealsSnapshot.empty) {
      const availableDatesSnapshot = await db
        .collection('users')
        .doc(userId)
        .collection('days')
        .orderBy('date', 'asc')
        .select('date')
        .get();

      if (!availableDatesSnapshot.empty) {
        const firstDoc = availableDatesSnapshot.docs[0];
        const lastDoc = availableDatesSnapshot.docs[availableDatesSnapshot.docs.length - 1];
        const firstDate = firstDoc.data().date.toDate().toISOString().split('T')[0];
        const lastDate = lastDoc.data().date.toDate().toISOString().split('T')[0];

        console.log('Available meal dates range from:', firstDate, 'to:', lastDate);

        const error = new Error(`No meals found for ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}. Available meals range from ${firstDate} to ${lastDate}.`);
        error.code = 'NO_MEALS_FOUND';
        error.availableDateRange = {firstDate, lastDate};
        throw error;
      }
    }

    if (mealsSnapshot.empty) {
      return [];
    }

    const mealsData = [];
    mealsSnapshot.forEach((doc) => {
      const dayData = doc.data();
      const date = dayData.date.toDate();

      console.log('Processing day document:', doc.id, 'Date:', date.toISOString(), 'Meals count:', dayData.meals?.length || 0);

      if (dayData.meals && Array.isArray(dayData.meals)) {
        dayData.meals.forEach((meal) => {
          mealsData.push({
            date: date,
            name: meal.name,
            type: meal.type,
            ingredients: meal.ingredients || [],
            description: meal.description || '',
          });
        });
      }
    });

    console.log('Total meals extracted:', mealsData.length);
    return mealsData;
  } catch (error) {
    console.error('Error fetching meals from date range:', error);
    throw error;
  }
}

/**
 * Generate shopping list using OpenAI
 * @param {string} userId - User ID (needed for generating document IDs)
 * @param {Array} mealsData - Array of meals data
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {string} preferredLanguage - User's preferred language
 * @return {Object} Generated shopping list data
 */
async function generateShoppingListWithOpenAI(userId, mealsData, startDate, endDate, preferredLanguage = 'ar') {
  try {
    // Create prompt using the utility function
    const prompt = createShoppingListPrompt(mealsData, startDate, endDate, preferredLanguage);

    // Use abstract OpenAI service for shopping list generation
    const responseText = await OpenAIService.createShoppingList(prompt, preferredLanguage);

    // Parse the OpenAI response
    const rawShoppingListData = parseShoppingListResponse(responseText);

    // Generate Firestore document ID for shopping list
    const shoppingListId = generateUserSubcollectionDocumentId(userId, 'shopping_list_entries');
    const currentTimestamp = Timestamp.now();

    // Process categories and items to add unique IDs
    const categoriesWithIds = rawShoppingListData.categories.map((category, categoryIndex) => {
      const categoryId = mapArabicNameToCategoryId(category.arabic_name);

      const itemsWithIds = category.items.map((item, itemIndex) => {
        // Generate unique item ID using Firestore document ID + category/item indices
        const itemId = `${shoppingListId}_${categoryIndex}_${itemIndex}`;

        return {
          id: itemId,
          name: item.name,
          quantity: item.quantity,
          unit: item.unit,
          is_purchased: false,
        };
      });

      // Get default icon for category if not provided or undefined
      const defaultIcon = getDefaultIconForCategory(categoryId);
      const categoryIcon = category.icon && category.icon !== 'undefined' ? category.icon : defaultIcon;

      return {
        id: categoryId,
        name: categoryId, // Use English category name for consistency
        arabic_name: category.arabic_name,
        items: itemsWithIds,
        icon: categoryIcon,
      };
    });

    // Construct final shopping list data with all required fields
    const shoppingListData = {
      title: rawShoppingListData.title,
      last_modified: currentTimestamp,
      id: shoppingListId,
      categories: categoriesWithIds,
      synced_at: currentTimestamp,
      created_at: currentTimestamp,
      notes: rawShoppingListData.notes,
      start_date: startDate, // Store as Date object
      end_date: endDate, // Store as Date object
    };

    console.log('Generated shopping list with', shoppingListData.categories?.length || 0, 'categories');
    console.log('Shopping list ID:', shoppingListId);

    return shoppingListData;
  } catch (error) {
    console.error('Error generating shopping list with OpenAI:', error);
    throw error;
  }
}

/**
 * Save shopping list to Firestore as a single document
 * @param {string} userId - User ID
 * @param {Object} shoppingListData - Shopping list data to save
 */
async function saveShoppingListToFirestore(userId, shoppingListData) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    // Save the complete shopping list as a single document
    const shoppingListRef = db
      .collection('users')
      .doc(userId)
      .collection('shopping_list_entries')
      .doc(shoppingListData.id);

    // Sanitize data for Firestore (timestamps are already Firestore Timestamp objects)
    // const firestoreData = sanitizeDataForFirestore(shoppingListData);

    await shoppingListRef.set(shoppingListData);

    const totalItems = shoppingListData.categories.reduce((total, category) =>
      total + (category.items?.length || 0), 0,
    );

    console.log('Successfully saved complete shopping list to Firestore:', {
      id: shoppingListData.id,
      title: shoppingListData.title,
      totalItems: totalItems,
      categories: shoppingListData.categories.length,
    });
  } catch (error) {
    console.error('Error saving shopping list to Firestore:', error);
    throw error;
  }
}

/**
 * Create notification for shopping list generation completion
 * @param {string} userId - User ID
 * @param {string} shoppingListTitle - Shopping list title
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {string} shoppingListId - Shopping list ID
 */
async function createShoppingListCompletionNotification(userId, shoppingListTitle, startDate, endDate, shoppingListId) {
  try {
    // Get user's preferred language for date formatting
    const userPreferredLanguage = await fetchUserPreferredLanguage(userId);

    // Format date range for notification message
    const dateRange = formatDateRange(startDate, endDate, userPreferredLanguage);

    // Create notification using the generic notification service
    await createNotification(userId, {
      type: NOTIFICATION_TYPES.SHOPPING_LIST_READY,
      content: {
        title: shoppingListTitle,
        dateRange: dateRange,
      },
      action: {
        type: 'internalNavigation',
        route: '/shopping-list',
        parameters: {
          shoppingListId: shoppingListId,
          openNewList: true,
        },
      },
      customId: 'shopping_list',
    });

    console.log('Shopping list notification created successfully for user:', userId);
  } catch (error) {
    console.error('Error creating shopping list notification:', error);
    // Don't throw error to avoid breaking the main shopping list generation flow
  }
}


/**
 * Validate that meals exist in the specified date range before starting generation
 * @param {string} userId - User ID
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @return {Object} Validation result with meal count
 */
async function validateMealsExistInDateRange(userId, startDate, endDate) {
  try {
    const mealsData = await fetchMealsFromDateRange(userId, startDate, endDate);

    if (!mealsData || mealsData.length === 0) {
      const error = new Error('No meals found for the specified date range');
      error.code = 'NO_MEALS_FOUND';
      throw error;
    }

    return {
      mealCount: mealsData.length,
      message: `Found ${mealsData.length} meals in date range`,
    };
  } catch (error) {
    console.error('Error validating meals in date range:', error);

    // Re-throw with proper error code if not already set
    if (!error.code) {
      error.code = 'VALIDATION_ERROR';
      error.message = 'Failed to validate meals in date range';
    }
    throw error;
  }
}

/**
 * Handle shopping list replacement logic
 * @param {string} userId - User ID
 * @param {string} shoppingIdToReplace - Shopping list ID to replace
 * @return {number} New replacement counter value
 */
async function handleShoppingListReplacement(userId, shoppingIdToReplace) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    // Get the existing shopping list document
    const existingDocRef = db
      .collection('users')
      .doc(userId)
      .collection('shopping_list_entries')
      .doc(shoppingIdToReplace);

    const existingDoc = await existingDocRef.get();

    if (!existingDoc.exists) {
      const error = new Error('Shopping list not found for replacement');
      error.code = 'SHOPPING_LIST_NOT_FOUND';
      throw error;
    }

    const existingData = existingDoc.data();
    const currentCounter = existingData.replacement_counter || 0;

    // Check if replacement limit exceeded
    if (currentCounter >= 3) {
      const error = new Error(
        'لقد تم استبدال قائمة التسوق هذه 3 مرات بالفعل. لا يمكن استبدالها مرة أخرى. يرجى إنشاء قائمة تسوق جديدة بدلاً من ذلك.',
      );
      error.code = 'REPLACEMENT_LIMIT_EXCEEDED';
      error.details = {
        currentCounter,
        maxAllowed: 3,
        shoppingListId: shoppingIdToReplace,
      };
      throw error;
    }

    // Delete the existing shopping list
    await existingDocRef.delete();
    console.log('Deleted existing shopping list for replacement:', shoppingIdToReplace);

    // Return incremented counter
    const newCounter = currentCounter + 1;
    console.log('Shopping list replacement counter:', newCounter);

    return newCounter;
  } catch (error) {
    console.error('Error handling shopping list replacement:', error);
    throw error;
  }
}

module.exports = {
  generateShoppingListFromMeals,
  validateMealsExistInDateRange,
};
